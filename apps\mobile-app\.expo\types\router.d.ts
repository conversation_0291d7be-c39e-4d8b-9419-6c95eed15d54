/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/../navigation/types`; params?: Router.UnknownInputParams; } | { pathname: `/../screens/auth/RegisterScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../screens/features/NotificationsScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../screens/features/OrderManagementScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../screens/features/TableManagementScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../context/CheckoutContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/FoodContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/NotificationContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/OrderContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/OrderItemContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/SloganContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/TableContext`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/add-order` | `/orders/add-order`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/tables/index` | `/tables/index`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in/index` | `/check-in/index`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in/ScanQRCode` | `/check-in/ScanQRCode`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in/session-detail` | `/check-in/session-detail`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications/index` | `/notifications/index`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in` | `/check-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/edit-order` | `/orders/edit-order`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/payment` | `/orders/payment`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/tables` | `/tables`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/../navigation/types`; params?: Router.UnknownOutputParams; } | { pathname: `/../screens/auth/RegisterScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/../screens/features/NotificationsScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/../screens/features/OrderManagementScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/../screens/features/TableManagementScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/../context/CheckoutContext`; params?: Router.UnknownOutputParams; } | { pathname: `/../context/FoodContext`; params?: Router.UnknownOutputParams; } | { pathname: `/../context/NotificationContext`; params?: Router.UnknownOutputParams; } | { pathname: `/../context/OrderContext`; params?: Router.UnknownOutputParams; } | { pathname: `/../context/OrderItemContext`; params?: Router.UnknownOutputParams; } | { pathname: `/../context/SloganContext`; params?: Router.UnknownOutputParams; } | { pathname: `/../context/TableContext`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders/add-order` | `/orders/add-order`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/tables/index` | `/tables/index`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/check-in/index` | `/check-in/index`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/check-in/ScanQRCode` | `/check-in/ScanQRCode`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/check-in/session-detail` | `/check-in/session-detail`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/notifications/index` | `/notifications/index`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/check-in` | `/check-in`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders/edit-order` | `/orders/edit-order`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders/payment` | `/orders/payment`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/tables` | `/tables`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/../navigation/types${`?${string}` | `#${string}` | ''}` | `/../screens/auth/RegisterScreen${`?${string}` | `#${string}` | ''}` | `/../screens/features/NotificationsScreen${`?${string}` | `#${string}` | ''}` | `/../screens/features/OrderManagementScreen${`?${string}` | `#${string}` | ''}` | `/../screens/features/TableManagementScreen${`?${string}` | `#${string}` | ''}` | `/../context/CheckoutContext${`?${string}` | `#${string}` | ''}` | `/../context/FoodContext${`?${string}` | `#${string}` | ''}` | `/../context/NotificationContext${`?${string}` | `#${string}` | ''}` | `/../context/OrderContext${`?${string}` | `#${string}` | ''}` | `/../context/OrderItemContext${`?${string}` | `#${string}` | ''}` | `/../context/SloganContext${`?${string}` | `#${string}` | ''}` | `/../context/TableContext${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders/add-order${`?${string}` | `#${string}` | ''}` | `/orders/add-order${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/tables/index${`?${string}` | `#${string}` | ''}` | `/tables/index${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/check-in/index${`?${string}` | `#${string}` | ''}` | `/check-in/index${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/check-in/ScanQRCode${`?${string}` | `#${string}` | ''}` | `/check-in/ScanQRCode${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/check-in/session-detail${`?${string}` | `#${string}` | ''}` | `/check-in/session-detail${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/notifications/index${`?${string}` | `#${string}` | ''}` | `/notifications/index${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/login${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/register${`?${string}` | `#${string}` | ''}` | `/register${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/check-in${`?${string}` | `#${string}` | ''}` | `/check-in${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/notifications${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders/edit-order${`?${string}` | `#${string}` | ''}` | `/orders/edit-order${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders${`?${string}` | `#${string}` | ''}` | `/orders${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders/payment${`?${string}` | `#${string}` | ''}` | `/orders/payment${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/tables${`?${string}` | `#${string}` | ''}` | `/tables${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/../navigation/types`; params?: Router.UnknownInputParams; } | { pathname: `/../screens/auth/RegisterScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../screens/features/NotificationsScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../screens/features/OrderManagementScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../screens/features/TableManagementScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../context/CheckoutContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/FoodContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/NotificationContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/OrderContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/OrderItemContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/SloganContext`; params?: Router.UnknownInputParams; } | { pathname: `/../context/TableContext`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/add-order` | `/orders/add-order`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/tables/index` | `/tables/index`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in/index` | `/check-in/index`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in/ScanQRCode` | `/check-in/ScanQRCode`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in/session-detail` | `/check-in/session-detail`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications/index` | `/notifications/index`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in` | `/check-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/edit-order` | `/orders/edit-order`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/payment` | `/orders/payment`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/tables` | `/tables`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
