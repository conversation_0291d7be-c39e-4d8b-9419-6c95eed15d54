import { <PERSON>, Get, Post, Body, <PERSON>, Param, Delete } from '@nestjs/common'
import { NotificationsService } from './notifications.service'
import { CreateNotificationDto, UpdateNotificationDto } from './notification.dto'

@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  create(@Body() createNotificationDto: CreateNotificationDto) {
    return this.notificationsService.create(createNotificationDto)
  }

  @Get()
  findAll() {
    return this.notificationsService.findAll()
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.notificationsService.findOne(id)
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateNotificationDto: UpdateNotificationDto) {
    return this.notificationsService.update(id, updateNotificationDto)
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.notificationsService.remove(id)
  }
}