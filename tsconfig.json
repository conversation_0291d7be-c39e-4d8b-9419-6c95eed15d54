{
  "compilerOptions": {
    "baseUrl": ".", // <PERSON><PERSON><PERSON> là thư mục hiện tại (monorepo root)
    "paths": {
      "@restaurant/shared/*": ["packages/shared/*"] // <PERSON><PERSON> đ<PERSON> import
    },
    "module": "commonjs",
    "target": "ES2020",
    "moduleResolution": "node",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "jsx": "react", // Thêm cấu hình JSX
    "jsxFactory": "React.createElement", // Chỉ định factory function cho JSX
    "jsxFragmentFactory": "React.Fragment", // Chỉ định factory function cho Fragment
    "types": ["react-native", "node", "multer"]
  }
}
