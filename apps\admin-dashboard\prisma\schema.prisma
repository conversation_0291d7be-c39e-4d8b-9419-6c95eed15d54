generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Employee {
  id            String         @id @default(uuid())
  email         String         @unique
  password      String
  name          String
  role          String
  isActive      Boolean        @default(true)
  createdAt     DateTime       @default(now())
  attendances   Attendance[]
  notifications NotificationAssignment[]
  orders        Order[]
  checkouts     checkouts[]


}

model Shift {
  id          String       @id @default(uuid())
  name        String
  startTime   DateTime
  endTime     DateTime
  attendances Attendance[]
}

model Attendance {
  id         String    @id @default(uuid())
  employeeId String
  shiftId    String
  checkIn    DateTime
  checkOut   DateTime?
  createdAt  DateTime  @default(now())
  employee   Employee  @relation(fields: [employeeId], references: [id])
  shift      Shift     @relation(fields: [shiftId], references: [id])
}

model Table {
  id     String      @id @default(uuid())
  number Int         @unique
  status TableStatus @default(AVAILABLE)
  orders Order[]
}

model Order {
  id          String      @id @default(uuid())
  tableId     String
  employeeId  String
  status      OrderStatus @default(RESERVED)
  createdAt   DateTime    @default(now())
  timeOut     DateTime?
  employee    Employee    @relation(fields: [employeeId], references: [id])
  table       Table       @relation(fields: [tableId], references: [id])
  orderItems  OrderItem[]
}

model OrderItem {
  id         String   @id @default(uuid())
  orderId    String
  foodId     String
  quantity   Int
  status     OrderItemStatus @default(PENDING)
  food       Food     @relation(fields: [foodId], references: [id])
  order      Order    @relation(fields: [orderId], references: [id])
}

model Food {
  id          String      @id @default(uuid())
  name        String
  price       Float
  category    FoodCategory
  isAvailable Boolean     @default(true)
  orderItems  OrderItem[]
  imageUrl    String?
}

model Notification {
  id         String   @id @default(uuid())
  title      String   @default("Notification")
  message    String
  createdAt  DateTime @default(now())
  NotificationAssignments NotificationAssignment[]
}

model NotificationAssignment {
  id            String   @id @default(uuid())
  notificationId String
  employeeId    String
  isRead        Boolean  @default(false)
  isDelete      Boolean  @default(false)
  notification  Notification @relation(fields: [notificationId], references: [id])
  employee      Employee   @relation(fields: [employeeId], references: [id])
}

model checkouts {
  id          String   @id @default(uuid())
  employee    Employee @relation(fields: [employeeId], references: [id])
  employeeId  String
  checkIn     DateTime @default(now())
  checkOut    DateTime?
  status      String   @default("CHECKED_IN")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model slogan {
  id          String   @id @default(uuid())
  content     String
  isVisible   Boolean  @default(true)
  createdAt   DateTime @default(now())
}

model Otp {
  id        String   @id @default(uuid())
  email     String
  code      String
  createdAt DateTime @default(now())
  expiresAt DateTime
  isActive  Boolean  @default(true)
}

model QRCode {
  id        String   @id @default(uuid())
  code      String   @unique
  validUntil DateTime
  location  String?
  isUsed    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt


}


enum Role {
  WAITER
  CASHIER
  MANAGER
  ADMIN
}

enum TableStatus {
  AVAILABLE
  OCCUPIED
  RESERVED
  CLEANING
}

enum FoodCategory {
  MAIN_COURSE
  APPETIZER
  DESSERT
  BEVERAGE
  SOUP
  SALAD
  SIDE_DISH
}

enum OrderItemStatus {
  PENDING
  PREPARING
  READY
  COMPLETE
}

enum OrderStatus {
  RESERVED
  PAID
}
